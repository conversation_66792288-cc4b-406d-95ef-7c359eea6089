"""
N8N Provider for webhook-based async processing
Handles script_generation, voice_generation, and image_prompt_generation stages
"""
import logging
import requests
import uuid
from typing import Dict, Any, Optional
from django.conf import settings

from .base import BaseServiceProvider, ProviderResponse, ProcessingStatus, ProviderError, CallbackMixin

logger = logging.getLogger(__name__)


class N8NProvider(BaseServiceProvider, CallbackMixin):
    """
    Provider for N8N webhook-based processing
    Supports async workflow execution with callback handling
    """
    
    # Stages supported by N8N
    SUPPORTED_STAGES = [
        'script_generation',
        'voice_generation', 
        'image_prompt_generation'
    ]
    
    # N8N workflow endpoint mappings
    N8N_ENDPOINTS = {
        'script_generation': 'script_generation',
        'voice_generation': 'voice_generation',
        'image_prompt_generation': 'image_prompt_generation'
    }
    
    # Callback URL mappings
    CALLBACK_ENDPOINTS = {
        'script_generation': 'script_generation',
        'voice_generation': 'voice_generation',
        'image_prompt_generation': 'image_prompt_generation'
    }
    
    def __init__(self):
        super().__init__('n8n')
        self.base_url = getattr(settings, 'N8N_WEBHOOK_BASE_URL', '') + getattr(settings, 'N8N_CREATE_VIDEO_API', '')
        self.callback_base_url = getattr(settings, 'CALLBACK_BASE_URL', 'https://videoai.syncu.in/api/videos/callback/recieve-response/')
        self.timeout = getattr(settings, 'N8N_REQUEST_TIMEOUT', 30)
    
    def get_supported_stages(self) -> list:
        """Get list of stages this provider supports"""
        return self.SUPPORTED_STAGES.copy()
    
    def process_stage(
        self, 
        video, 
        stage: str, 
        correlation_id: str, 
        payload: Optional[Dict[str, Any]] = None
    ) -> ProviderResponse:
        """
        Process a stage using N8N webhook
        
        Args:
            video: Video model instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            payload: Optional additional payload data
            
        Returns:
            ProviderResponse: Response with pending status for async operation
        """
        try:
            self.log_operation('process_stage', video.id, stage, {
                'correlation_id': correlation_id
            })
            
            # Validate stage
            if not self.validate_stage(stage):
                raise ProviderError(
                    f"Stage '{stage}' not supported by N8N provider",
                    self.provider_name,
                    stage,
                    'UNSUPPORTED_STAGE'
                )
            
            # Prepare payload
            stage_payload = self.prepare_payload(video, stage)
            
            # Add correlation tracking
            stage_payload.update({
                'correlation_id': correlation_id,
                'video_id': str(video.id),
                'stage': stage
            })
            
            # Merge with additional payload if provided
            if payload:
                stage_payload.update(payload)
            
            # Get URLs
            webhook_url = self._get_webhook_url(stage, video.id)
            n8n_url = self._get_n8n_url(stage)
            
            # Add webhook URL to payload
            stage_payload['webhook_url'] = webhook_url
            
            # Make N8N request
            response = self._make_n8n_request(n8n_url, stage_payload)
            
            # Generate execution ID
            execution_id = str(uuid.uuid4())
            
            # Update video with execution tracking
            video.latest_execution_id = execution_id
            video.stage = stage
            video.save()
            
            # Return pending response for async operation
            return ProviderResponse.pending(
                execution_id=execution_id,
                metadata={
                    'n8n_url': n8n_url,
                    'webhook_url': webhook_url,
                    'request_payload': stage_payload
                }
            )
            
        except ProviderError:
            raise
        except Exception as e:
            logger.error(f"N8N provider error for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def prepare_payload(self, video, stage: str) -> Dict[str, Any]:
        """
        Prepare stage-specific payload for N8N
        
        Args:
            video: Video model instance
            stage: Stage name
            
        Returns:
            Dict: N8N-specific payload
        """
        # Serialize account data properly
        account_details = None
        if video.account:
            account_details = {
                'id': video.account.id,
                'name': video.account.name,
                'topic': video.account.topic,
                'platforms': video.account.platforms,
                'credentials': video.account.credentials,
                'language': video.account.language,
                'status': video.account.status,
            }
        
        # Base payload
        payload = {
            'id': f'{video.id}',
            'video_type': video.video_type,
            'script_type': video.script_type,
            'orientation': video.orientation,
            'language': video.language,
            'duration': video.duration,
            'stage': stage,
            'account_details': account_details
        }
        
        # Add stage-specific data
        if stage == 'script_generation':
            payload.update({
                'context': video.context,
            })
        elif stage == 'voice_generation':
            payload.update({
                'script': video.script,
                'speech_type': video.speech_type,
                'tts_provider': video.tts_provider,
            })
            
            # Add TTS voice details
            if video.tts_voice:
                payload['tts_voice'] = {
                    'id': video.tts_voice.id,
                    'name': video.tts_voice.name,
                    'provider_name': video.tts_voice.provider_name,
                    'language': video.tts_voice.language,
                }
        elif stage == 'image_prompt_generation':
            payload.update({
                'script': video.script,
                'video_style': video.video_style,
            })
        
        return payload
    
    def handle_response(
        self, 
        response: Any, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle N8N webhook response (called by callback handler)
        
        Args:
            response: Response data from N8N callback
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized response
        """
        try:
            # N8N responses come through callbacks
            # This method processes the callback data
            
            if isinstance(response, dict):
                # Check for success indicators
                if response.get('status') == 'success' or response.get('success'):
                    return ProviderResponse.success(
                        data=response,
                        execution_id=response.get('execution_id'),
                        metadata={'stage': stage, 'provider': 'n8n'}
                    )
                else:
                    error_message = response.get('error', 'N8N processing failed')
                    return ProviderResponse.failure(
                        error_message=error_message,
                        error_code=response.get('error_code', 'N8N_PROCESSING_ERROR'),
                        execution_id=response.get('execution_id')
                    )
            else:
                return ProviderResponse.failure(
                    error_message="Invalid response format from N8N",
                    error_code='INVALID_RESPONSE_FORMAT'
                )
                
        except Exception as e:
            logger.error(f"Error handling N8N response for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def handle_error(
        self, 
        error: Exception, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle N8N provider errors
        
        Args:
            error: Exception that occurred
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized error response
        """
        error_message = str(error)
        error_code = 'N8N_PROVIDER_ERROR'
        
        # Categorize different types of errors
        if isinstance(error, requests.exceptions.Timeout):
            error_code = 'N8N_TIMEOUT_ERROR'
            error_message = f"N8N request timeout for stage {stage}"
        elif isinstance(error, requests.exceptions.ConnectionError):
            error_code = 'N8N_CONNECTION_ERROR'
            error_message = f"Failed to connect to N8N for stage {stage}"
        elif isinstance(error, requests.exceptions.RequestException):
            error_code = 'N8N_REQUEST_ERROR'
            error_message = f"N8N request failed for stage {stage}: {error_message}"
        elif isinstance(error, ProviderError):
            error_code = error.error_code or 'N8N_PROVIDER_ERROR'
            error_message = error.message
        
        self.log_operation('handle_error', video.id, stage, {
            'error_code': error_code,
            'error_message': error_message
        })
        
        return ProviderResponse.failure(
            error_message=error_message,
            error_code=error_code,
            metadata={
                'provider': self.provider_name,
                'stage': stage,
                'video_id': video.id
            }
        )
    
    def _get_n8n_url(self, stage: str) -> str:
        """Get N8N webhook URL for stage"""
        endpoint = self.N8N_ENDPOINTS.get(stage)
        if not endpoint:
            raise ProviderError(
                f"No N8N endpoint found for stage: {stage}",
                self.provider_name,
                stage,
                'MISSING_ENDPOINT'
            )
        return f"{self.base_url}{endpoint}"
    
    def _get_webhook_url(self, stage: str, video_id) -> str:
        """Get callback webhook URL for stage"""
        callback_endpoint = self.CALLBACK_ENDPOINTS.get(stage)
        if not callback_endpoint:
            raise ProviderError(
                f"No callback endpoint found for stage: {stage}",
                self.provider_name,
                stage,
                'MISSING_CALLBACK_ENDPOINT'
            )
        # Remove trailing slash from base URL to avoid double slashes
        base_url = self.callback_base_url.rstrip('/')
        return f"{base_url}/{video_id}/{callback_endpoint}"
    
    def _make_n8n_request(self, url: str, payload: Dict[str, Any]) -> dict:
        """
        Make HTTP request to N8N webhook
        
        Args:
            url: N8N webhook URL
            payload: Request payload
            
        Returns:
            dict: Response data
            
        Raises:
            requests.RequestException: If request fails
        """
        try:
            logger.info(f"Making N8N request to {url}")
            
            response = requests.post(
                url,
                json=payload,
                timeout=self.timeout,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'AIVIA-VideoAgent/1.0'
                }
            )
            
            response.raise_for_status()
            
            # N8N webhook calls typically return 200 with minimal response
            # The actual processing happens asynchronously
            return response.json() if response.content else {'status': 'initiated'}
            
        except requests.exceptions.RequestException as e:
            logger.error(f"N8N request failed: {str(e)}")
            raise
    
    def validate_callback_data(self, data: Dict[str, Any], stage: str) -> bool:
        """
        Validate N8N callback data format
        
        Args:
            data: Callback data
            stage: Stage name
            
        Returns:
            bool: True if data is valid
        """
        required_fields = {
            'script_generation': ['script'],
            'voice_generation': ['speech_url'],
            'image_prompt_generation': ['image_prompts']
        }
        
        stage_fields = required_fields.get(stage, [])
        return all(field in data for field in stage_fields)
    
    def process_callback_data(self, video, stage: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process N8N callback data and update video models
        
        Args:
            video: Video model instance
            stage: Stage name
            data: Callback data from N8N
            
        Returns:
            Dict: Processing result
        """
        try:
            if stage == 'script_generation':
                return self._process_script_generation(video, data)
            elif stage == 'voice_generation':
                return self._process_voice_generation(video, data)
            elif stage == 'image_prompt_generation':
                return self._process_image_prompt_generation(video, data)
            else:
                raise ValueError(f"Unsupported stage: {stage}")
                
        except Exception as e:
            self.logger.error(f"Error processing {stage} callback: {e}")
            raise
    
    def _process_script_generation(self, video, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process script generation callback data"""
        # Update video with script data
        if 'script' in data:
            video.script = data['script']
        
        if 'title' in data:
            video.title = data['title']
        
        if 'description' in data:
            video.description = data['description']
        
        video.save()
        self.logger.info(f"Updated script data for video {video.id}")
        
        return {
            'data_processed': ['script', 'title', 'description'],
            'message': 'Script generation data processed successfully'
        }
    
    def _process_voice_generation(self, video, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process voice generation callback data"""
        # Update video with voice/speech data
        if 'speech_url' in data:
            video.speech_url = data['speech_url']
        
        if 'avatar_url' in data:
            video.avatar_url = data['avatar_url']
        
        video.save()
        self.logger.info(f"Updated voice data for video {video.id}")
        
        return {
            'data_processed': ['speech_url', 'avatar_url'],
            'message': 'Voice generation data processed successfully'
        }
    
    def _process_image_prompt_generation(self, video, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process image prompt generation callback data"""
        from videos.models import MediaGeneration
        
        # Get existing MediaGeneration entries created during clip creation
        media_generations = MediaGeneration.objects.filter(
            video=video,
            media_type='image',
            prompt=""  # Only update entries that don't have prompts yet
        ).order_by('prompt_sequence')
        
        prompts = data.get('image_prompts', [])
        updated_count = 0
        
        # Update existing MediaGeneration entries with generated prompts
        for index, prompt_data in enumerate(prompts):
            if index < media_generations.count():
                media_gen = media_generations[index]
                media_gen.prompt = prompt_data.get('prompt', '')
                media_gen.save()
                updated_count += 1
                self.logger.info(f"Updated MediaGeneration {media_gen.id} with prompt for sequence {media_gen.prompt_sequence}")
            else:
                # Create new MediaGeneration if there are more prompts than existing entries
                MediaGeneration.objects.create(
                    video=video,
                    prompt=prompt_data.get('prompt', ''),
                    input_prompt="",  # No input prompt for additional entries
                    media_type='image',
                    media_provider=video.image_provider or 'default',
                    prompt_sequence=prompt_data.get('sequence_order', index + 1),
                )
                updated_count += 1
                self.logger.info(f"Created additional MediaGeneration for sequence {index + 1}")
        
        self.logger.info(f"Updated/created {updated_count} image prompts for video {video.id}")
        
        return {
            'prompts_updated': updated_count,
            'message': f'Updated/created {updated_count} image prompts'
        }
